package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/18 10:09
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DistributionResultCauseEnum {
    CUSTOMER_SERVICE_CANCELLATION(1, "客服取消"),
    ORDER_RETURN(2, "订单回退"),
    ORDER_MATCHING_IS_TEMPORARILY_SUSPENDED(3, "订单暂停匹配"),
    DURING_THE_MODEL_S_SCHEDULE(4, "模特行程中"),
    MODEL_SUSPENDS_COLLABORATION(5, "模特暂停合作"),
    MODEL_CANCELS_THE_COLLABORATION(6, "模特取消合作"),
    UNCONFIRMED(7, "未确认"),
    TRANSACTION_CLOSED(8, "交易关闭"),
    MODEL_HAS_BEEN_REMOVED(9, "模特已下架"),
    MOD<PERSON>_HAS_OVERDUE_ORDER(10, "模特有逾期订单"),
    MERCHANT_BLACKLISTS_MODEL(11, "商家拉黑模特"),
    ;
    private Integer code;
    private String label;
}
