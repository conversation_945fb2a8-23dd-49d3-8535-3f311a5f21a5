package com.wnkx.order.service.impl

import cn.hutool.core.collection.CollUtil
import cn.hutool.core.date.DateUtil
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ruoyi.common.core.constant.OrderConstant
import com.ruoyi.common.core.enums.*
import com.ruoyi.common.core.utils.SpringUtils
import com.ruoyi.common.redis.service.RedisService
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO
import com.wnkx.order.config.OrderVideoProperties
import com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper
import com.wnkx.order.remote.RemoteService
import com.wnkx.order.service.*
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Shared
import spock.lang.Specification

import java.lang.reflect.Field

/**
 * OrderVideoMatchPreselectModelServiceImpl 测试类
 * 专门测试 cleanupInvalidPreselectModels 方法
 */
class OrderVideoMatchPreselectModelServiceImplTest extends Specification {

    // Mock 依赖对象
    RemoteService remoteService = Mock()
    IOrderVideoModelService orderVideoModelService = Mock()
    OrderVideoProperties orderVideoProperties = Mock()
    IOrderService orderService = Mock()
    RedisService redisService = Mock()
    OrderResourceService orderResourceService = Mock()
    IOrderVideoContentService videoContentService = Mock()
    OrderVideoMatchService orderVideoMatchService = Mock()
    OrderVideoMatchPreselectModelMapper baseMapper = Mock()

    // 被测试的服务实例
    OrderVideoMatchPreselectModelServiceImpl service

    @Shared
    MockedStatic springUtils

    def setupSpec() {
        springUtils = Mockito.mockStatic(SpringUtils.class)
    }

    def cleanupSpec() {
        springUtils.close()
    }

    def setup() {
        // 创建服务实例并注入所有必需的依赖
        service = new OrderVideoMatchPreselectModelServiceImpl(
            remoteService,
            orderVideoModelService,
            orderVideoProperties,
            orderService,
            redisService,
            orderResourceService,
            videoContentService
        )
        
        // 使用反射注入 baseMapper
        Field baseMapperField = ServiceImpl.getDeclaredField("baseMapper")
        baseMapperField.setAccessible(true)
        baseMapperField.set(service, baseMapper)
        
        // Mock SpringUtils.getBean 调用
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(orderVideoMatchService)
    }

    def "cleanupInvalidPreselectModels - 正常清理流程测试"() {
        given: "准备测试数据"
        // 模拟活跃的预选模特数据
        List<OrderVideoMatchPreselectModel> allActivePreselectModels = createActivePreselectModels()
        
        // 模拟状态异常的模特
        List<ModelInfoVO> invalidModels = createInvalidModels()
        
        // 模拟逾期订单的模特ID
        List<Long> overdueModelIds = [2L, 3L]
        
        // 模拟被拉黑的预选模特
        List<OrderVideoMatchPreselectModel> blacklistedModels = createBlacklistedModels()

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> allActivePreselectModels
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> overdueModelIds
        baseMapper.selectBlacklistedPreselectModels() >> blacklistedModels
        baseMapper.updateBatchById(_) >> true
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 5 // 总共清理5条记录
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        1 * baseMapper.updateBatchById(_)
        1 * orderVideoMatchService.clearFlag(_, _)
    }

    def "cleanupInvalidPreselectModels - 无数据需要清理"() {
        given: "准备空数据"
        List<OrderVideoMatchPreselectModel> emptyActiveModels = []
        List<ModelInfoVO> emptyInvalidModels = []
        List<Long> emptyOverdueModelIds = []
        List<OrderVideoMatchPreselectModel> emptyBlacklistedModels = []

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> emptyActiveModels
        remoteService.getModelsForPreselectCleanup() >> emptyInvalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> emptyOverdueModelIds
        baseMapper.selectBlacklistedPreselectModels() >> emptyBlacklistedModels

        int result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 0
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        0 * orderVideoModelService.checkModelOverdueVideo(_) // 因为没有活跃数据，不会调用
        1 * baseMapper.selectBlacklistedPreselectModels()
        0 * baseMapper.updateBatchById(_) // 没有数据需要更新
        0 * orderVideoMatchService.clearFlag(_, _) // 没有数据需要清理标记
    }

    def "cleanupInvalidPreselectModels - 异常处理测试"() {
        given: "模拟异常情况"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()

        when: "远程服务调用异常"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> { throw new RuntimeException("远程服务异常") }
        orderVideoModelService.checkModelOverdueVideo(_) >> []
        baseMapper.selectBlacklistedPreselectModels() >> []

        int result = service.cleanupInvalidPreselectModels()

        then: "验证异常处理"
        result == 0 // 异常情况下返回0
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        0 * baseMapper.updateBatchById(_)
        0 * orderVideoMatchService.clearFlag(_, _)
    }

    def "cleanupInvalidPreselectModels - 只有状态异常模特需要清理"() {
        given: "只有状态异常的模特"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()
        List<ModelInfoVO> invalidModels = createInvalidModels()

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> []
        baseMapper.selectBlacklistedPreselectModels() >> []
        baseMapper.updateBatchById(_) >> true
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 2 // 只清理状态异常的2条记录
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        1 * baseMapper.updateBatchById(_)
        1 * orderVideoMatchService.clearFlag(_, _)
    }

    // 辅助方法：创建活跃的预选模特数据
    private List<OrderVideoMatchPreselectModel> createActivePreselectModels() {
        return [
            createPreselectModel(1L, 1001L, 1L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()),
            createPreselectModel(2L, 1002L, 2L, PreselectStatusEnum.JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()),
            createPreselectModel(3L, 1003L, 3L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.DISTRIBUTION.getCode()),
            createPreselectModel(4L, 1004L, 4L, PreselectStatusEnum.JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()),
            createPreselectModel(5L, 1005L, 5L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode())
        ]
    }

    // 辅助方法：创建状态异常的模特数据
    private List<ModelInfoVO> createInvalidModels() {
        return [
            createModelInfoVO(1L, ModelStatusEnum.PAUSE.getCode(), 1), // 暂停合作
            createModelInfoVO(2L, ModelStatusEnum.CANCEL.getCode(), 1) // 取消合作
        ]
    }

    // 辅助方法：创建被拉黑的预选模特数据
    private List<OrderVideoMatchPreselectModel> createBlacklistedModels() {
        return [
            createPreselectModel(6L, 1006L, 6L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode())
        ]
    }

    // 辅助方法：创建预选模特对象
    private OrderVideoMatchPreselectModel createPreselectModel(Long id, Long matchId, Long modelId, Integer status, Integer addType) {
        OrderVideoMatchPreselectModel model = new OrderVideoMatchPreselectModel()
        model.id = id
        model.matchId = matchId
        model.modelId = modelId
        model.status = status
        model.addType = addType
        model.addTime = DateUtil.date()
        return model
    }

    // 辅助方法：创建模特信息对象
    private ModelInfoVO createModelInfoVO(Long id, Integer status, Integer isShow) {
        ModelInfoVO model = new ModelInfoVO()
        model.id = id
        model.status = status
        model.isShow = isShow
        return model
    }

    def "cleanupInvalidPreselectModels - 只有逾期订单模特需要清理"() {
        given: "只有逾期订单的模特"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()
        List<Long> overdueModelIds = [2L, 3L]

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> []
        orderVideoModelService.checkModelOverdueVideo(_) >> overdueModelIds
        baseMapper.selectBlacklistedPreselectModels() >> []
        baseMapper.updateBatchById(_) >> true
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 2 // 清理逾期的2条记录
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        1 * baseMapper.updateBatchById(_)
        1 * orderVideoMatchService.clearFlag(_, _)
    }

    def "cleanupInvalidPreselectModels - 只有被拉黑模特需要清理"() {
        given: "只有被拉黑的模特"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()
        List<OrderVideoMatchPreselectModel> blacklistedModels = createBlacklistedModels()

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> []
        orderVideoModelService.checkModelOverdueVideo(_) >> []
        baseMapper.selectBlacklistedPreselectModels() >> blacklistedModels
        baseMapper.updateBatchById(_) >> true
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 1 // 清理被拉黑的1条记录
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        1 * baseMapper.updateBatchById(_)
        1 * orderVideoMatchService.clearFlag(_, _)
    }

    def "cleanupInvalidPreselectModels - 验证清理逻辑的正确性"() {
        given: "准备测试数据"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()
        List<ModelInfoVO> invalidModels = createInvalidModels()
        List<Long> overdueModelIds = [2L]
        List<OrderVideoMatchPreselectModel> blacklistedModels = createBlacklistedModels()

        // 用于捕获传递给updateBatchById的参数
        List<OrderVideoMatchPreselectModel> updatedModels = []

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> overdueModelIds
        baseMapper.selectBlacklistedPreselectModels() >> blacklistedModels
        baseMapper.updateBatchById(_) >> { List<OrderVideoMatchPreselectModel> models ->
            updatedModels.addAll(models)
            return true
        }
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证清理的数据正确性"
        result == 4 // 状态异常2条 + 逾期1条 + 拉黑1条
        updatedModels.size() == 4

        // 验证状态都被设置为已淘汰
        updatedModels.every { it.status == PreselectStatusEnum.OUT.getCode() }

        // 验证备注信息正确设置
        updatedModels.find { it.modelId == 1L }?.remark != null
        updatedModels.find { it.modelId == 2L }?.remark == OrderConstant.ORDER_PRESELECT_MODEL_OVERDUE_REMARK
        updatedModels.find { it.modelId == 6L }?.remark == OrderConstant.ORDER_PRESELECT_MODEL_BLACK_REMARK
    }

    def "cleanupInvalidPreselectModels - 测试不同添加类型的处理逻辑"() {
        given: "包含不同添加类型的预选模特"
        List<OrderVideoMatchPreselectModel> activeModels = [
            createPreselectModel(1L, 1001L, 1L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()),
            createPreselectModel(2L, 1002L, 2L, PreselectStatusEnum.JOINTED.getCode(), PreselectModelAddTypeEnum.DISTRIBUTION.getCode()),
            createPreselectModel(3L, 1003L, 3L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.INTENTION_MODEL.getCode())
        ]
        List<Long> overdueModelIds = [1L, 2L, 3L]

        List<OrderVideoMatchPreselectModel> updatedModels = []

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> []
        orderVideoModelService.checkModelOverdueVideo(_) >> overdueModelIds
        baseMapper.selectBlacklistedPreselectModels() >> []
        baseMapper.updateBatchById(_) >> { List<OrderVideoMatchPreselectModel> models ->
            updatedModels.addAll(models)
            return true
        }
        orderVideoMatchService.clearFlag(_, _) >> {}

        int result = service.cleanupInvalidPreselectModels()

        then: "验证不同添加类型的处理"
        result == 3
        updatedModels.size() == 3

        // 验证OPERATION类型的模特设置了selectStatus和selectTime
        def operationModel = updatedModels.find { it.addType == PreselectModelAddTypeEnum.OPERATION.getCode() }
        operationModel.selectStatus == OrderVideoModelSelectStatusEnum.CANCEL.getCode()
        operationModel.selectTime != null

        // 验证非OPERATION类型的模特没有设置selectStatus
        def nonOperationModels = updatedModels.findAll { it.addType != PreselectModelAddTypeEnum.OPERATION.getCode() }
        nonOperationModels.every { it.selectStatus == null }
    }

    def "cleanupInvalidPreselectModels - 数据库更新失败测试"() {
        given: "准备测试数据"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()
        List<ModelInfoVO> invalidModels = createInvalidModels()

        when: "数据库更新失败"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> []
        baseMapper.selectBlacklistedPreselectModels() >> []
        baseMapper.updateBatchById(_) >> { throw new RuntimeException("数据库更新失败") }

        then: "应该抛出异常"
        thrown(RuntimeException)
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        1 * baseMapper.updateBatchById(_)
        0 * orderVideoMatchService.clearFlag(_, _) // 更新失败时不应该调用清理标记
    }

    def "cleanupInvalidPreselectModels - 空集合和null值处理"() {
        given: "包含null和空集合的情况"
        List<OrderVideoMatchPreselectModel> activeModels = createActivePreselectModels()

        when: "返回null或空集合"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> null
        orderVideoModelService.checkModelOverdueVideo(_) >> null
        baseMapper.selectBlacklistedPreselectModels() >> null

        int result = service.cleanupInvalidPreselectModels()

        then: "应该正确处理null值"
        result == 0
        1 * baseMapper.selectList(_ as LambdaQueryWrapper)
        1 * remoteService.getModelsForPreselectCleanup()
        1 * orderVideoModelService.checkModelOverdueVideo(_)
        1 * baseMapper.selectBlacklistedPreselectModels()
        0 * baseMapper.updateBatchById(_)
        0 * orderVideoMatchService.clearFlag(_, _)
    }

    def "cleanupInvalidPreselectModels - 验证匹配单标记清理"() {
        given: "准备测试数据"
        List<OrderVideoMatchPreselectModel> activeModels = [
            createPreselectModel(1L, 1001L, 1L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()),
            createPreselectModel(2L, 1001L, 2L, PreselectStatusEnum.JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()), // 同一个匹配单
            createPreselectModel(3L, 1002L, 3L, PreselectStatusEnum.UN_JOINTED.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode()) // 不同匹配单
        ]
        List<ModelInfoVO> invalidModels = [
            createModelInfoVO(1L, ModelStatusEnum.PAUSE.getCode(), 1),
            createModelInfoVO(2L, ModelStatusEnum.PAUSE.getCode(), 1),
            createModelInfoVO(3L, ModelStatusEnum.PAUSE.getCode(), 1)
        ]

        List<Long> clearedMatchIds = []

        when: "调用清理方法"
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activeModels
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        orderVideoModelService.checkModelOverdueVideo(_) >> []
        baseMapper.selectBlacklistedPreselectModels() >> []
        baseMapper.updateBatchById(_) >> true
        orderVideoMatchService.clearFlag(_, _) >> { List<Long> matchIds, Integer flag ->
            clearedMatchIds.addAll(matchIds)
        }

        int result = service.cleanupInvalidPreselectModels()

        then: "验证匹配单标记清理"
        result == 3
        clearedMatchIds.size() == 2 // 去重后的匹配单ID数量
        clearedMatchIds.containsAll([1001L, 1002L])
        1 * orderVideoMatchService.clearFlag(_, 1) // flag参数应该是1
    }
}
