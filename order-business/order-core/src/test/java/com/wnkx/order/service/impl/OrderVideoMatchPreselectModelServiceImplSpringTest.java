package com.wnkx.order.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import com.wnkx.order.service.OrderVideoMatchService;
import com.wnkx.order.service.IOrderVideoModelService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OrderVideoMatchPreselectModelServiceImpl Spring Boot 测试类
 * 使用 @SpringBootTest 注解进行集成测试
 */
@SpringBootTest
@ActiveProfiles("dev") // 使用测试配置文件
public class OrderVideoMatchPreselectModelServiceImplSpringTest {

    @Resource
    private IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;

    @MockBean
    private OrderVideoMatchPreselectModelMapper baseMapper;

    @MockBean
    private RemoteService remoteService;

    @MockBean
    private IOrderVideoModelService orderVideoModelService;

    @MockBean
    private OrderVideoMatchService orderVideoMatchService;

    private MockedStatic<com.ruoyi.common.core.utils.SpringUtils> springUtilsMock;


    @Test
    void test1() {
        orderVideoMatchPreselectModelService.cleanupInvalidPreselectModels();
    }


}
