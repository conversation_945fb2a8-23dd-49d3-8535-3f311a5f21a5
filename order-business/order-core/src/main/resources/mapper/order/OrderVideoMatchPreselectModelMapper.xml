<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel" id="OrderVideoPreselectModelResult">
        <result property="id"    column="id"    />
        <result property="addType"    column="add_type"    />
        <result property="addUserId"    column="add_user_id"    />
        <result property="addTime"    column="add_time"    />
        <result property="modelId"    column="model_id"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="oustType"    column="oust_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderVideoPreselectModelVo">
        select id, video_id, add_type, add_user_id, add_time, model_id, status, remark, oust_type, create_time, update_time from order_video_match_preselect_model
    </sql>

    <sql id="selectDistributionSql">
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.rollback_id &lt;=&gt; ovm.rollback_id
        WHERE
            ovm.`status` = ${@<EMAIL>}
            AND ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
            AND ovmpm.add_type = ${@<EMAIL>}
            AND ovmpm.`status` IN (
                ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getCode},
                ${@<EMAIL>}
            )
            AND ovmpm.model_intention = ${@com.ruoyi.common.core.enums.ModelIntentionEnum@MT_UN_CONFIRM.getCode}
            AND ovmpm.distribution_result = ${@<EMAIL>}
            AND NOT EXISTS (
                SELECT
                    1
                FROM
                    order_video_match_preselect_model ovmpm_sub
                WHERE
                    ovmpm_sub.match_id = ovmpm.match_id
                    AND ovmpm_sub.STATUS = ${@<EMAIL>}
            )
    </sql>

    <select id="selectListVOByMatchIds"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoMatchPreselectModelVO">
        SELECT
        ov.id videoId,
        ov.create_order_biz_user_id bizUserId,
        ovmpm.id,
        ovmpm.match_id,
        ovmpm.add_type,
        ovmpm.add_time,
        ovmpm.model_id,
        ovmpm.model_type,
        ovmpm.model_platform,
        ovmpm.model_cooperation,
        ovmpm.model_person_name,
        ovmpm.`status`,
        ovmpm.remark,
        ovmpm.oust_type,
        ovmpm.add_user_id,
        ovmpm.model_intention
        FROM
        order_video_match_preselect_model ovmpm
        JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
        join order_video ov on ov.id = ovm.video_id
        where ovmpm.match_id IN
        <foreach collection="matchIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectEnglishWorkbenchStatistics" resultType="com.ruoyi.system.api.domain.vo.order.EnglishStatisticsVO">
        SELECT
            COUNT(CASE WHEN ovmpm.status = ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getCode} THEN 1 END) unContactCount,
            COUNT(CASE WHEN ovmpm.status = ${@<EMAIL>} THEN 1 END) contactingCount
        FROM
            order_video_match_preselect_model ovmpm
                JOIN (
                SELECT *,
                       ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY id DESC) AS rn
                FROM order_video_match
            ) ovm ON ovm.id = ovmpm.match_id AND ovm.rn = 1
        inner join order_video ov on ov.id = ovm.video_id
        <where>
            ovm.status = ${@<EMAIL>}
            and ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
            AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getcode}
            and ovm.end_time IS NULL
            <if test="dto.modelIds != null and dto.modelIds.size() > 0 ">
                AND ovmpm.model_id IN
                <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectMyPreselectDockingList"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel">
        SELECT
            ovmpm.id,
            ovmpm.match_id,
            ovmpm.add_type,
            ovmpm.add_time,
            ovmpm.model_id,
            ovmpm.model_type,
            ovmpm.model_platform,
            ovmpm.model_cooperation,
            ovmpm.model_cooperation_score,
            ovmpm.model_person_name,
            ovmpm.`status`,
            ovmpm.remark,
            ovmpm.oust_type,
            ovmpm.add_user_id,
            ovmpm.selected_time,
            ovmpm.model_intention
        FROM
            order_video_match_preselect_model ovmpm
            JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
        <where>
            ovmpm.`status` != ${@<EMAIL>}
            AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}

            AND (
                ovmpm.add_type != ${@<EMAIL>}
                OR (ovmpm.add_type = ${@<EMAIL>} AND ovmpm.model_intention = ${@<EMAIL>})
            )

            <if test="dto.matchIds != null and dto.matchIds.size() > 0 ">
                AND ovmpm.match_id IN
                <foreach collection="dto.matchIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelIntentions != null and dto.modelIntentions.size() > 0">
                AND ovmpm.model_intention IN
                <foreach collection="dto.modelIntentions" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.isPendingSubmit == true ">
                AND ovmpm.status = #{dto.status}
            </if>

            <if test="dto.statusList != null and dto.statusList.size() > 0">
                AND ovmpm.status IN
                <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.addPreselectTimes != null and dto.addPreselectTimes.size() > 0 and dto.isPendingSubmit == true">
                AND (
                <foreach item="value" index="key" collection="dto.addPreselectTimeMap.entrySet()" separator=" or " >
                    date_format(ovmpm.add_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                </foreach>
                )
            </if>

            <if test="dto.addTypes != null and dto.addTypes.size() > 0 and dto.isPendingSubmit == true ">
                AND ovmpm.add_type IN
                <foreach collection="dto.addTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectModelIds != null and dto.preselectModelIds.size() > 0 and dto.isPendingSubmit == true ">
                AND ovmpm.model_id IN
                <foreach collection="dto.preselectModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectModelIds != null and dto.preselectModelIds.size() > 0 ">
                AND ovmpm.model_id IN
                <foreach collection="dto.preselectModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="checkExistSelectedModelByVideoIdAndRollbackId" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
        WHERE
            ovm.video_id = #{videoId}
          <if test="rollbackId != null">
              AND ovm.rollback_id = #{rollbackId}
          </if>
          AND ovmpm.`status` = ${@<EMAIL>}
    </select>
    <select id="myPreselectDockingModelSelect" resultType="java.lang.Long">
        SELECT
            ovmpm.model_id
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
        WHERE
            ovmpm.`status` != ${@<EMAIL>}
          AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
          AND ovmpm.match_id IN (
            SELECT
                ovm.id
            FROM
                order_video_match ovm
                    JOIN order_video ov ON ov.id = ovm.video_id
                    AND ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
                    AND ovm.end_time IS NULL
        )
        <if test="currentUserRelevanceModelIds != null and currentUserRelevanceModelIds.size()  > 0 ">
            AND ovmpm.model_id IN
            <foreach collection="currentUserRelevanceModelIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectMyPreselectDistributionModelListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectDistributionListVO">
        SELECT
            ovmpm.model_id,
            GROUP_CONCAT( ovmpm.id ) AS ovmpmIds
        <include refid="selectDistributionSql"/>
        <if test="dto.keyword != null and dto.keyword != '' ">
            AND (
                LOWER(ov.video_code) LIKE LOWER(CONCAT( '%', #{dto.keyword}, '%' ))
                OR LOWER(ov.product_chinese) LIKE LOWER(CONCAT( '%', #{dto.keyword}, '%' ))
                OR LOWER(ov.product_english) LIKE LOWER(CONCAT( '%', #{dto.keyword}, '%' ))
                OR LOWER(ov.create_order_user_name) LIKE LOWER(CONCAT( '%', #{dto.keyword}, '%' ))
                OR LOWER(ov.create_order_user_nick_name) LIKE LOWER(CONCAT( '%', #{dto.keyword}, '%' ))
            )
        </if>
        <if test="dto.communicationStatus != null">
            AND ovmpm.status = #{dto.communicationStatus}
        </if>
        <if test="dto.modelIds != null and dto.modelIds.size() > 0">
            AND ovmpm.model_id IN
            <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.addPreselectTimes != null and dto.addPreselectTimes.size() > 0">
            AND (
            <foreach item="value" index="key" collection="dto.addPreselectTimeMap.entrySet()" separator=" or " >
                DATE_FORMAT(ovmpm.add_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
            </foreach>
            )
        </if>
        GROUP BY
            ovmpm.model_id
        ORDER BY
            MAX( ovmpm.add_time ) DESC
    </select>
    <select id="selectMyPreselectDistributionOrderListByOvmpmIds"
            resultType="com.ruoyi.system.api.domain.vo.order.DistributionOrderListVO">
        SELECT
            ov.id AS video_id,
            ovm.id AS match_id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.refund_pic_count,
            ov.reference_pic AS referencePicId,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            ov.contact_id,
            ov.video_duration,
            ov.video_style,
            ov.reference_video_link,
            ov.is_gund,
            ovmpm.add_time,
            ovmpm.`status`,
            ovmpm.model_id,
            ovmpm.id AS preselectModelId
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.rollback_id &lt;=&gt; ovm.rollback_id
        WHERE
            ovmpm.id IN
            <foreach item="item" collection="ovmpmIds" separator="," close=")" open="(" index="index">
                #{item}
            </foreach>
    </select>
    <select id="selectDistributionHistoryListByModelId"
            resultType="com.ruoyi.system.api.domain.vo.order.DistributionHistoryListVO">
        SELECT
            ovmpm.id AS preselectModelId,
            ov.video_code,
            ov.product_chinese,
            ovmpm.add_time,
            ovmpm.distribution_result,
            ovmpm.distribution_result_cause
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                JOIN order_video ov ON ov.id = ovm.video_id
        WHERE
            model_id = #{modelId}
          AND add_type = ${@<EMAIL>()}
          AND distribution_result != ${@<EMAIL>()}
        ORDER BY
            distribution_result_time DESC
    </select>
    <select id="myPreselectDistributionModelSelect" resultType="java.lang.Long">
        SELECT
            DISTINCT (ovmpm.model_id)
        <include refid="selectDistributionSql"/>
    </select>
    <select id="distributionIssueList" resultType="com.ruoyi.common.core.domain.vo.UserVO">
        SELECT DISTINCT
            ovm.model_person_id AS id,
            ovm.model_person_name AS name
        FROM
            order_video_match_preselect_model ovm
        WHERE
            ovm.model_person_id IS NOT NULL
          AND ovm.model_person_name IS NOT NULL
        ORDER BY
            ovm.model_person_name
    </select>

    <!-- 查询被商家拉黑的预选模特记录 -->
    <select id="selectBlacklistedPreselectModels" resultMap="OrderVideoPreselectModelResult">
        SELECT
            ovmpm.id,
            ovmpm.match_id,
            ovmpm.add_type,
            ovmpm.add_user_id,
            ovmpm.add_time,
            ovmpm.model_id,
            ovmpm.status,
            ovmpm.remark,
            ovmpm.oust_type,
            ovmpm.oust_time,
            ovmpm.select_status,
            ovmpm.select_time,
            ovmpm.model_intention,
            ovmpm.distribution_result,
            ovmpm.distribution_result_cause,
            ovmpm.distribution_result_time,
            ovmpm.model_person_id,
            ovmpm.model_person_name,
            ovmpm.need_remind_shoot_attention,
            ovmpm.select_timeout,
            ovmpm.create_time,
            ovmpm.update_time
        FROM
            order_video_match_preselect_model ovmpm
            INNER JOIN order_video_match ovm ON ovmpm.match_id = ovm.id
            INNER JOIN order_video ov ON ovm.video_id = ov.id AND (ov.rollback_id &lt;=&gt; ovm.rollback_id)
            INNER JOIN user_model_blacklist umb ON umb.model_id = ovmpm.model_id
                AND umb.biz_user_id = ov.create_order_biz_user_id
        WHERE
            ovmpm.status IN (
                ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getCode},
                ${@<EMAIL>}
            )
    </select>
</mapper>